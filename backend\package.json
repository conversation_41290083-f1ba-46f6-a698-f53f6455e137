{"name": "backend", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"body-parser": "^1.20.2", "cheerio": "^1.1.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.19.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "node-cron": "^4.1.0", "nodemailer": "^6.9.15", "open": "^10.1.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.16.0"}}